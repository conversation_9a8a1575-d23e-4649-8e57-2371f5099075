# CustomEmpty 空状态组件

一个美观的空状态组件，支持自定义文字和多种主题样式。

## 功能特点

- 🎨 美观的设计，符合项目设计规范
- 📱 响应式布局，适配不同屏幕尺寸
- 🎭 多种主题和状态样式
- ✨ 流畅的动画效果
- 🔧 高度可定制

## 基本用法

```tsx
import CustomEmpty from '@/components/CustomEmpty';

// 基本用法
<CustomEmpty text="暂无数据" />

// 带描述文字
<CustomEmpty 
  text="暂无推荐数据" 
  description="暂时没有短途航线推荐" 
/>
```

## Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| text | string | '暂无数据' | 主要文字 |
| description | string | - | 描述文字（可选） |
| className | string | '' | 自定义样式类名 |

## 预设样式

### 加载状态
```tsx
<CustomEmpty 
  text="加载中..." 
  description="正在获取推荐数据，请稍候" 
  className="loading-state"
/>
```

### 错误状态
```tsx
<CustomEmpty 
  text="加载失败" 
  description="网络连接异常，请稍后重试"
  className="error-state"
/>
```

### 紧凑模式
```tsx
<CustomEmpty 
  text="暂无数据" 
  className="compact"
/>
```

### 主题变体
```tsx
// 深色主题
<CustomEmpty 
  text="暂无数据" 
  className="theme-dark"
/>

// 浅色主题（默认）
<CustomEmpty 
  text="暂无数据" 
  className="theme-light"
/>
```

## 设计说明

- **图标设计**: 使用纯 CSS 绘制的文档图标，简洁美观
- **颜色方案**: 遵循项目的颜色变量规范
- **动画效果**: 渐入动画和加载状态的脉冲动画
- **响应式**: 在小屏幕设备上自动调整尺寸

## 样式定制

可以通过传入 `className` 来自定义样式：

```less
.my-custom-empty {
  .icon-circle {
    background: linear-gradient(135deg, #your-color-1, #your-color-2);
  }
  
  .empty-text {
    color: #your-text-color;
  }
}
```

## 在项目中的使用

该组件已经集成到 `RecommendList` 组件中，用于处理：
- 加载状态
- 错误状态  
- 空数据状态

可以在其他需要空状态展示的地方复用此组件。
