@import '../../styles/variables.less';

.custom-empty {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  min-height: 200px;

  .empty-icon {
    margin-bottom: 24px;

    .empty-image {
      width: 120px;
    }
  }

  .empty-text {
    font-size: 16px;
    color: @color-gray-66;
    margin-bottom: 8px;
    text-align: center;
    line-height: 1.4;
    opacity: 0.8;
  }

  .empty-description {
    font-size: 14px;
    color: @color-gray-99;
    text-align: center;
    line-height: 1.5;
  }

  // 动画效果
  .empty-icon {
    animation: fadeInUp 0.6s ease-out;
  }

  .empty-text {
    animation: fadeInUp 0.6s ease-out 0.1s both;
  }

  .empty-description {
    animation: fadeInUp 0.6s ease-out 0.2s both;
  }
}

// 动画定义
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 主题变体
.custom-empty {
  // 浅色主题（默认）
  &.theme-light {
    .icon-circle {
      background: linear-gradient(135deg, #f5f6fa 0%, #e8f0ff 100%);
    }
  }

  // 深色主题
  &.theme-dark {
    .icon-circle {
      background: linear-gradient(135deg, #2a2d3a 0%, #1a1d29 100%);

      .icon-line {
        background: #4a5568;
      }
    }

    .empty-text {
      color: #e2e8f0;
    }

    .empty-description {
      color: #a0aec0;
    }
  }

  // 紧凑模式
  &.compact {
    padding: 32px 16px;
    min-height: 120px;

    .empty-icon {
      margin-bottom: 16px;

      .icon-circle {
        width: 56px;
        height: 56px;
      }
    }

    .empty-text {
      font-size: 14px;
      margin-bottom: 4px;
    }

    .empty-description {
      font-size: 12px;
    }
  }

  // 加载状态
  &.loading-state {
    .icon-circle {
      background: linear-gradient(135deg, #e8f0ff 0%, #d1e7ff 100%);
      animation: pulse 2s ease-in-out infinite;

      .icon-line {
        background: @color-primary;
        opacity: 0.8;
      }
    }

    .empty-text {
      color: @color-primary;
    }
  }
}
