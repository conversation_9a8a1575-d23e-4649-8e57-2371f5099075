import { memo } from 'react';
import { View, Text } from '@tarojs/components';
import './index.less';
import { Loading1 } from '@nutui/icons-react-taro';

interface CustomEmptyProps {
  text?: string;
  description?: string;
  className?: string;
  type?: 'default' | 'loading';
}

const CustomEmpty = ({
  text = '暂无数据',
  description,
  className = '',
  type = 'default',
}: CustomEmptyProps) => {
  return (
    <View className={`custom-empty ${className}`}>
      {/* 空状态图标 */}
      <View className='empty-icon'>
        {type === 'default' && <></>}
        {type === 'loading' && <Loading1 size={24} />}
      </View>

      {/* 主要文字 */}
      <Text className='empty-text'>{text}</Text>

      {/* 描述文字 */}
      {description && <Text className='empty-description'>{description}</Text>}
    </View>
  );
};

export default memo(CustomEmpty);
