import { memo } from 'react';
import { Image, Text, View } from '@tarojs/components';
import { jaunt } from '@/utils/img';
import './index.less';

const RecommendList = () => {
  return (
    <>
      <View className={'recommend-list'}>
        {Array(4)
          .fill('')
          .map((_item, index) => {
            return (
              <View className={'recommend-item'} key={index}>
                {/*<View*/}
                {/*  className={'is-top'}*/}
                {/*  style={{*/}
                {/*    background: `url(${recommend}) no-repeat center center/cover`,*/}
                {/*  }}*/}
                {/*>*/}
                {/*  <View className={'top-txt'}>TOP</View>*/}
                {/*  <View className={'top-num'}>01</View>*/}
                {/*</View>*/}
                <Image src={jaunt} />
                <View className={'recommend-airline'}>
                  <View className={'airline-name-box'}>
                    <Text className={'airline'}>成都-绵阳</Text>
                    <Text className={'price'}>¥ 480</Text>
                  </View>
                  <View className={'desc'}>
                    <Text>空客H135</Text>
                    <View className={'split-line'}></View>
                    <View className={'seat'}>5-6人</View>
                  </View>
                </View>
              </View>
            );
          })}
      </View>
    </>
  );
};
export default memo(RecommendList);
