import { TOKEN_NAME } from '@/utils/constant';
import Taro from '@tarojs/taro';
import { _localStorage, clearUserSession, toUrl } from '@/utils';
import { toLowerCase } from '@tarojs/components/lib/vue2/vue-component-lib/utils';

interface RequestOptions {
  path: string;
  method?: keyof Taro.request.Method;
  type?: any;
  body?: any;
  query?: any;
  format?: any;
  secure?: boolean;
}

// Token管理
const getToken = () => {
  const token = _localStorage.getItem(TOKEN_NAME);
  return token ? { Authorization: `Bearer ${token}` } : {};
};

// 检查token是否过期
const isTokenExpired = () => {
  const accessTokenTimeOut = _localStorage.getItem('accessTokenTimeOut');
  if (!accessTokenTimeOut) return true;

  const expireTime = new Date(accessTokenTimeOut).getTime();
  const currentTime = new Date().getTime();

  // 提前5分钟刷新token
  return currentTime >= expireTime - 5 * 60 * 1000;
};

// 检查refresh token是否过期
const isRefreshTokenExpired = () => {
  const refreshTokenTimeOut = _localStorage.getItem('refreshTokenTimeOut');
  if (!refreshTokenTimeOut) return true;

  const expireTime = new Date(refreshTokenTimeOut).getTime();
  const currentTime = new Date().getTime();

  return currentTime >= expireTime;
};

// 刷新token的并发控制
let isRefreshing = false;
let failedQueue: Array<{ resolve: Function; reject: Function }> = [];

const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error);
    } else {
      resolve(token);
    }
  });
  failedQueue = [];
};

const refreshToken = async (): Promise<boolean> => {
  if (isRefreshing) {
    // 如果正在刷新，将请求加入队列
    return new Promise((resolve, reject) => {
      failedQueue.push({ resolve, reject });
    });
  }

  isRefreshing = true;

  try {
    const refreshTokenValue = _localStorage.getItem('refreshToken');
    const uid = _localStorage.getItem('uid');

    if (!refreshTokenValue || !uid || isRefreshTokenExpired()) {
      throw new Error('Refresh token无效或已过期');
    }

    const response = await Taro.request({
      url: SERVER_URL + '/common/v1.0/refresh',
      method: 'POST',
      data: {
        refreshToken: refreshTokenValue,
        uid: uid,
      },
      header: {
        'content-type': 'application/json',
        appSign: 'HXGW',
      },
    });

    if (response.statusCode === 200 && response.data?.success && response.data?.data) {
      const {
        accessToken,
        refreshToken: newRefreshToken,
        accessTokenTimeOut,
        refreshTokenTimeOut,
      } = response.data.data;

      // 更新存储的token信息
      _localStorage.setItem(TOKEN_NAME, accessToken);
      _localStorage.setItem('refreshToken', newRefreshToken);
      _localStorage.setItem('accessTokenTimeOut', accessTokenTimeOut);
      _localStorage.setItem('refreshTokenTimeOut', refreshTokenTimeOut);

      processQueue(null, accessToken);
      return true;
    } else {
      throw new Error('刷新token失败');
    }
  } catch (error) {
    console.error('刷新token失败:', error);
    processQueue(error, null);
    // 清空所有token信息
    clearUserSession();
    // 跳转到登录页
    toUrl('/pages/login/index');
    return false;
  } finally {
    isRefreshing = false;
  }
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const request = async <T = any, _E = any>(
  options: RequestOptions & {
    isloading?: boolean;
  }
): Promise<T> => {
  const { path, method = 'GET', body, query, type, isloading = false, ...params } = options;

  const token = _localStorage.getItem(TOKEN_NAME);
  const isLoginPage =
    toLowerCase(Taro.getCurrentPages()?.[0].route as string)?.indexOf('login') > 0; //是否登录页面

  if (!isLoginPage) {
    // 检查是否有token
    if (!token) {
      Taro.redirectTo({ url: '/pages/login/index' });
      throw new Error('未登录');
    }

    // 检查token是否过期
    if (isTokenExpired()) {
      if (isRefreshing) {
        // 如果正在刷新token，将请求加入队列
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        }).then(() => {
          // token刷新成功后重新发起请求
          return request(options);
        });
      }

      isRefreshing = true;

      try {
        const refreshSuccess = await refreshToken();
        if (!refreshSuccess) {
          processQueue(new Error('Token刷新失败'), null);
          throw new Error('Token刷新失败');
        }
        processQueue(null, _localStorage.getItem(TOKEN_NAME));
      } catch (error) {
        processQueue(error, null);
        throw error;
      } finally {
        isRefreshing = false;
      }
    }
  }

  try {
    // 请求拦截器
    isloading && Taro.showLoading({ title: '加载中...' });

    // 发起请求
    const response = await Taro.request({
      url: SERVER_URL + path,
      method: method,
      data: body || query,
      header: {
        'content-type': type || 'application/json',
        appSign: 'HXGW',
        uid: _localStorage.getItem('uid'),
        ...getToken(),
        ...((params as any)?.header ?? {}),
      },
      ...params,
    });

    isloading && Taro.hideLoading();

    // 处理返回
    if (response.statusCode >= 200 && response.statusCode < 300) {
      // 检查是否是401未授权错误
      if (response.data?.code === 401) {
        // token无效，尝试刷新
        if (!isRefreshing) {
          const refreshSuccess = await refreshToken();
          if (refreshSuccess) {
            // 刷新成功，重新发起请求
            return request(options);
          }
        }
        // 刷新失败或跳过认证的接口，返回错误
        throw new Error('认证失败，请重新登录');
      }

      return response.data;
    } else {
      handleError(response);
      return response?.data ?? {};
    }
  } catch (error) {
    isloading && Taro.hideLoading();
    handleError(error);
    throw error;
  }
};

const handleError = (error: any) => {
  console.error('请求错误:', error);
  let errorMessage = '请求失败，请稍后重试';

  if (error && error.statusCode) {
    switch (error.statusCode) {
      case 401:
        errorMessage = '认证失败，请重新登录';
        clearUserSession();
        break;
      case 403:
        errorMessage = '没有权限访问该资源';
        break;
      case 404:
        errorMessage = '请求的资源不存在';
        break;
      case 500:
        errorMessage = '请稍候再试';
        break;
      default:
        errorMessage = `请求错误 ${error.statusCode}`;
    }
  }

  // 只在非401错误时显示错误提示，401由token刷新机制处理
  if (!error || error.statusCode !== 401) {
    Taro.showToast({
      title: errorMessage,
      icon: 'none',
      duration: 2000,
    });
  }
};

export default request;
